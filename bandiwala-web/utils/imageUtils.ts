/**
 * Utility functions for handling image URLs
 */

// The base URL for the API server that serves images
const API_SERVER_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

// Default fallback image that is known to exist on the server
const DEFAULT_IMAGE = '/images/default-food.jpg';

// Log the API server URL for debugging
console.log('Image Utils: Using API_SERVER_URL:', API_SERVER_URL);

/**
 * Converts a relative image path to an absolute URL
 * @param imagePath The relative image path (e.g., "/images/vendor1.jpg")
 * @returns The absolute URL for the image
 */
export const getImageUrl = (imagePath: string): string => {
  console.log('getImageUrl called with:', imagePath);

  // If the path is already an absolute URL, return it as is
  if (imagePath && (imagePath.startsWith('http://') || imagePath.startsWith('https://'))) {
    console.log('Already absolute URL, returning as is');
    return imagePath;
  }

  // If the path doesn't exist or is invalid, use a default image
  if (!imagePath || imagePath === 'undefined' || imagePath === 'null') {
    console.log('Invalid path, using default image');
    return `${API_SERVER_URL}${DEFAULT_IMAGE}`;
  }

  // If the path starts with a slash, it's already properly formatted
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  console.log('Normalized path:', normalizedPath);

  // For paths with spaces and special characters, encode each segment properly
  let encodedPath;
  if (normalizedPath.includes(' ') || normalizedPath.includes('(') || normalizedPath.includes(')')) {
    console.log('Path contains spaces/special chars, encoding segments');
    // Split by '/', encode each segment, then rejoin
    encodedPath = normalizedPath.split('/').map(segment => {
      // Don't encode empty segments (from leading slash)
      return segment === '' ? '' : encodeURIComponent(segment);
    }).join('/');
    console.log('Encoded path:', encodedPath);
  } else {
    // For simple paths, use encodeURI
    encodedPath = encodeURI(normalizedPath);
    console.log('Simple encoding:', encodedPath);
  }

  // Always use the API server for images
  const finalUrl = `${API_SERVER_URL}${encodedPath}`;
  console.log('Final URL:', finalUrl);
  return finalUrl;
};

/**
 * Gets a safe fallback image based on the type
 * @param fallbackType The type of fallback image needed ('food' or 'vendor')
 * @returns The path to a safe fallback image
 */
export const getSafeFallbackImage = (fallbackType: 'food' | 'vendor' = 'food'): string => {
  const fallbackImages = {
    food: '/images/default-food.jpg',
    vendor: '/images/default-vendor.jpg'
  };

  return fallbackImages[fallbackType] || fallbackImages.food;
};