'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { cartService } from '../services/api';
import { Cart, CartTotals, CartItem } from '@/types/cart';
import { cartSync } from '@/utils/cartSync';
import { NetworkMonitor } from '@/utils/networkMonitor';

import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { isWithinDeliveryArea } from '@/utils/distance';

interface Subcategory {
  title: string;
  quantity: string;
  price: number;
}

interface CartContextType {
  cart: Cart;
  loading: boolean;
  error: string | null;
  isOnline: boolean;
  addToCart: (menuItemId: string, quantity?: number, notes?: string, selectedSubcategory?: Subcategory, userLocation?: { lat: number; lng: number }) => Promise<void>;
  updateCartItem: (menuItemId: string, quantity: number, notes?: string, selectedSubcategory?: Subcategory) => Promise<void>;
  removeFromCart: (menuItemId: string, selectedSubcategory?: Subcategory) => Promise<void>;
  clearCart: () => Promise<void>;
  reorderCartItems: (items: CartItem[]) => Promise<void>;
  reorderFromOrder: (orderItems: any[]) => Promise<void>;
  calculateCartTotals: () => CartTotals;
  refreshCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [cart, setCart] = useState<Cart>({ userId: '', items: [] });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [useLocalStorage, setUseLocalStorage] = useState(false);

  // Get the authenticated user from AuthContext
  const { user, isAuthenticated } = useAuth();

  // Initialize network monitor
  useEffect(() => {
    const networkMonitor = NetworkMonitor.getInstance();
    return () => networkMonitor.cleanup();
  }, []);

  // Load cart data when user changes
  useEffect(() => {
    const loadCart = async () => {
      try {
        console.log('Loading cart data...', { isAuthenticated, userId: user?._id || user?.phone });
        setLoading(true);
        setError(null);

        // Determine the user ID to use
        const userId = isAuthenticated && user ? (user._id || user.phone) : 'guest-user';
        console.log('Using userId for cart:', userId);

        // Try to get cart from server first
        try {
          console.log('Attempting to load cart from server...');

          const response = await cartService.getCart();
          console.log('Cart response from server:', response);

          // Check if we should use local storage based on server response
          if (response.useLocalStorage) {
            console.log('Server indicated to use local storage');
            setUseLocalStorage(true);

            // Get local cart and ensure it has the correct user ID
            const localCart = cartSync.getLocalCart();

            // Always update the userId in the local cart with the current user
            if (localCart && typeof localCart === 'object') {
              localCart.userId = userId;
              console.log('Updated local cart with userId:', localCart.userId);
            } else {
              console.error('Invalid local cart data from cartSync');
            }

            console.log('Local cart:', localCart);
            setCart(localCart || { userId: '', items: [] });
          } else {
            // Use server data but ensure it has the correct user ID
            console.log('Using server cart data');
            setUseLocalStorage(false);

            // Make sure the server cart has the correct user ID
            const serverCart = response.data;
            if (serverCart && typeof serverCart === 'object') {
              serverCart.userId = userId;
              setCart(serverCart);
            } else {
              // If server cart is invalid, fall back to local storage
              console.warn('Invalid server cart data, falling back to local storage');
              setUseLocalStorage(true);
              const localCart = cartSync.getLocalCart();
              if (localCart && typeof localCart === 'object') {
                localCart.userId = userId;
              }
              setCart(localCart || { userId: '', items: [] });
            }
          }
        } catch (serverErr) {
          console.error('Error getting cart from server, falling back to local storage:', serverErr);
          setUseLocalStorage(true);

          // Get local cart and ensure it has the correct user ID
          const localCart = cartSync.getLocalCart();
          if (localCart && typeof localCart === 'object') {
            localCart.userId = userId;
            console.log('Updated local cart with userId:', localCart.userId);
          } else {
            console.error('Invalid local cart data');
          }

          console.log('Local cart (fallback):', localCart);
          setCart(localCart || { userId: '', items: [] });
        }
      } catch (err) {
        console.error('Error loading cart:', err);
        setError('Failed to load cart');
        setUseLocalStorage(true);

        // Get local cart and ensure it has the correct user ID
        const localCart = cartSync.getLocalCart();
        const userId = isAuthenticated && user ? (user._id || user.phone) : 'guest-user';
        if (localCart && typeof localCart === 'object') {
          localCart.userId = userId;
          console.log('Updated local cart with userId:', localCart.userId);
        } else {
          console.error('Invalid local cart data in error handler');
        }

        setCart(localCart || { userId: '', items: [] });
      } finally {
        setLoading(false);
      }
    };

    loadCart();
  }, [isAuthenticated, user]);

  // Sync with localStorage when needed
  useEffect(() => {
    if (useLocalStorage) {
      cartSync.saveLocalCart(cart);
    }
  }, [cart, useLocalStorage]);

  const addToCart = async (menuItemId: string, quantity: number = 1, notes?: string, selectedSubcategory?: Subcategory, userLocation?: { lat: number; lng: number }) => {
    console.log('Adding to cart:', { menuItemId, quantity, notes, selectedSubcategory, userLocation });

    // Validate inputs
    if (!menuItemId || menuItemId.trim() === '') {
      setError('Invalid menu item ID. Please try again with a valid item.');
      console.error('Invalid menuItemId in addToCart: empty or undefined');
      return;
    }

    if (quantity <= 0) {
      setError('Quantity must be greater than zero.');
      console.error('Invalid quantity in addToCart:', quantity);
      return;
    }

    if (notes && notes.length > 500) {
      setError('Notes are too long. Please keep notes under 500 characters.');
      console.error('Notes too long in addToCart:', notes?.length);
      return;
    }

    // Validate selected subcategory
    if (!selectedSubcategory) {
      setError('Please select a plate size.');
      console.error('No subcategory selected in addToCart');
      return;
    }

    // Check if user location is within delivery area
    if (userLocation) {
      const isWithinArea = isWithinDeliveryArea(userLocation);
      if (!isWithinArea) {
        toast.error('Out of stock for your area', {
          description: 'This item is not available for delivery to your location. Please try a different location within our service area.',
          position: 'top-right',
          duration: 5000
        });
        console.log('User location outside delivery area:', userLocation);
        return;
      }
    } else {
      // If no location provided, try to get it from user's saved location
      if (user?.location?.coordinates) {
        const isWithinArea = isWithinDeliveryArea(user.location.coordinates);
        if (!isWithinArea) {
          toast.error('Out of stock for your area', {
            description: 'This item is not available for delivery to your saved location. Please update your location within our service area.',
            position: 'top-right',
            duration: 5000
          });
          console.log('User saved location outside delivery area:', user.location.coordinates);
          return;
        }
      } else {
        // No location available - warn user but allow adding to cart
        console.warn('No user location available for delivery area validation');
        // toast.warning('Location not detected', {
        //   description: 'Please ensure your location is within our delivery area before placing an order.',
        //   position: 'top-right',
        //   duration: 4000
        // });
      }
    }

    // Try to get item details from local storage
    let itemName = 'Item';
    let vendorId = '';
    let vendorName = 'Unknown Vendor';
    let vendorPhone = 'Not available';
    let itemImage = '/placeholder.jpg';

    try {
      const storedItemDetails = localStorage.getItem(`item-details-${menuItemId}`);
      if (!storedItemDetails) {
        console.log('No stored item details found, will use defaults');
      } else {
        console.log('Found stored item details:', JSON.parse(storedItemDetails));
        const parsedDetails = JSON.parse(storedItemDetails);
        if (parsedDetails.name) {
          itemName = parsedDetails.name;
        }
        if (parsedDetails.vendorId) {
          vendorId = parsedDetails.vendorId;
        }
        if (parsedDetails.vendorName) {
          vendorName = parsedDetails.vendorName;
        }
        if (parsedDetails.vendorPhone) {
          vendorPhone = parsedDetails.vendorPhone;
        }
        if (parsedDetails.image) {
          itemImage = parsedDetails.image;
        }
      }
    } catch (error) {
      console.error('Error checking stored item details:', error);
    }

    // Optional MongoDB ObjectId validation
    // Uncomment if you want to enforce MongoDB ID format
    // const isValidObjectId = /^[0-9a-fA-F]{24}$/.test(menuItemId);
    // if (!isValidObjectId) {
    //   setError('Invalid menu item ID format. Please try again with a valid item.');
    //   console.error('Invalid menuItemId format in addToCart:', menuItemId);
    //   return;
    // }

    // Set loading state
    setLoading(true);
    setError(null);

    // Sync with server or local storage
    try {
      if (useLocalStorage) {
        console.log('Adding to local storage cart');
        // Update cart locally
        setCart(prevCart => {
          const updatedCart = { ...prevCart };
          const existingItem = updatedCart.items.find(
            item => item.menuItemId === menuItemId &&
                    item.selectedSubcategory?.title === selectedSubcategory.title
          );

          if (existingItem) {
            existingItem.quantity += quantity;
            existingItem.notes = notes || existingItem.notes;
            itemName = existingItem.name;
          } else {
            updatedCart.items.push({
              menuItemId,
              quantity,
              notes: notes || '',
              selectedSubcategory,
              name: itemName,
              image: itemImage,
              vendorId: vendorId,
              vendorName: vendorName,
              vendorPhone: vendorPhone
            });
          }

          // Save to local storage
          cartSync.saveLocalCart(updatedCart);
          return updatedCart;
        });
      } else {
        console.log('Syncing with server...');
        // Call the server API to add the item
        const response = await cartService.addToCart(
          menuItemId,
          quantity,
          notes,
          undefined, // token will be handled by the service
          selectedSubcategory.title, // plate parameter
          userLocation // user location for delivery area validation
        );

        if (response.success && response.data) {
          // Update cart with server response to ensure consistency
          const serverCart = response.data;
          serverCart.userId = cart.userId; // Ensure userId is preserved
          setCart(serverCart);
          console.log('Cart updated from server response');

          // Get item name from server response for toast
          const addedItem = serverCart.items.find(item => item.menuItemId === menuItemId);
          if (addedItem) {
            itemName = addedItem.name;
          }
        } else {
          throw new Error(response.message || 'Failed to add item to cart');
        }
      }

      // Show success toast
      toast(`Item added to cart`, {
        description: `${quantity} ${itemName} ${quantity > 1 ? 'have' : 'has'} been added to your cart`,
        position: 'top-right',
        duration: 3000
      });

    } catch (error) {
      console.error('Error syncing cart:', error);

      // If server fails, fall back to local storage
      if (!useLocalStorage) {
        console.log('Server failed, falling back to local storage');
        setUseLocalStorage(true);

        // Add to local cart as fallback
        setCart(prevCart => {
          const updatedCart = { ...prevCart };
          const existingItem = updatedCart.items.find(
            item => item.menuItemId === menuItemId &&
                    item.selectedSubcategory?.title === selectedSubcategory.title
          );

          if (existingItem) {
            existingItem.quantity += quantity;
            existingItem.notes = notes || existingItem.notes;
          } else {
            updatedCart.items.push({
              menuItemId,
              quantity,
              notes: notes || '',
              selectedSubcategory,
              name: itemName,
              image: itemImage,
              vendorId: vendorId,
              vendorName: vendorName,
              vendorPhone: vendorPhone
            });
          }

          // Save to local storage
          cartSync.saveLocalCart(updatedCart);
          return updatedCart;
        });

        // Show success toast even for fallback
        toast(`Item added to cart`, {
          description: `${quantity} ${itemName} ${quantity > 1 ? 'have' : 'has'} been added to your cart (saved locally)`,
          position: 'top-right',
          duration: 3000
        });

        setError('Cart saved locally. Will sync when connection is restored.');
      } else {
        setError('Failed to save cart. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (menuItemId: string, quantity: number, notes?: string, selectedSubcategory?: Subcategory) => {
    console.log('Updating cart item:', { menuItemId, quantity, notes, selectedSubcategory });

    // Validate inputs
    if (!menuItemId || menuItemId.trim() === '') {
      setError('Invalid menu item ID. Please try again with a valid item.');
      console.error('Invalid menuItemId in updateCartItem: empty or undefined');
      return;
    }

    if (quantity < 0) {
      setError('Quantity cannot be negative.');
      console.error('Invalid quantity in updateCartItem:', quantity);
      return;
    }

    if (notes && notes.length > 500) {
      setError('Notes are too long. Please keep notes under 500 characters.');
      console.error('Notes too long in updateCartItem:', notes?.length);
      return;
    }

    // Check if the item exists in the cart
    const itemExists = selectedSubcategory
      ? cart.items.some(item => item.menuItemId === menuItemId && item.selectedSubcategory?.title === selectedSubcategory.title)
      : cart.items.some(item => item.menuItemId === menuItemId);

    if (!itemExists && quantity > 0) {
      console.error('Attempted to update non-existent item:', menuItemId, selectedSubcategory ? `with subcategory ${selectedSubcategory.title}` : '');
      setError('Cannot update an item that is not in your cart.');
      return;
    }

    // Set loading state
    setLoading(true);
    setError(null);

    // Optimistically update the UI
    setCart(prevCart => {
      const updatedCart = { ...prevCart };
      // Find the item with matching menuItemId and selectedSubcategory (if provided)
      const itemIndex = updatedCart.items.findIndex(item => {
        if (selectedSubcategory) {
          return item.menuItemId === menuItemId && item.selectedSubcategory?.title === selectedSubcategory.title;
        }
        return item.menuItemId === menuItemId;
      });

      if (itemIndex !== -1) {
        if (quantity <= 0) {
          updatedCart.items.splice(itemIndex, 1);
        } else {
          updatedCart.items[itemIndex] = {
            ...updatedCart.items[itemIndex],
            quantity,
            notes: notes || updatedCart.items[itemIndex].notes,
            selectedSubcategory: selectedSubcategory || updatedCart.items[itemIndex].selectedSubcategory
          };
        }
      }

      return updatedCart;
    });

    // Sync with server or local storage
    try {
      if (useLocalStorage) {
        console.log('Saving updated cart to local storage');
        cartSync.saveLocalCart(cart);
      } else {
        console.log('Syncing cart update with server...');
        const response = await cartService.updateCartItem(
          menuItemId,
          quantity,
          notes,
          undefined, // token will be handled by the service
          selectedSubcategory?.title // plate parameter
        );

        if (response.success && response.data) {
          // Update cart with server response to ensure consistency
          const serverCart = response.data;
          serverCart.userId = cart.userId;
          setCart(serverCart);
          console.log('Cart updated from server after update');
        } else {
          console.warn('Server update failed, keeping optimistic update');
        }
      }
    } catch (error) {
      console.error('Error syncing cart update:', error);
      if (!useLocalStorage) {
        console.log('Server failed, falling back to local storage');
        setUseLocalStorage(true);
        cartSync.saveLocalCart(cart);
      }
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (menuItemId: string, selectedSubcategory?: Subcategory) => {
    // Validate inputs
    if (!menuItemId || menuItemId.trim() === '') {
      setError('Invalid menu item ID. Please try again with a valid item.');
      console.error('Invalid menuItemId in removeFromCart: empty or undefined');
      return;
    }

    // Check if the item exists in the cart
    const itemExists = selectedSubcategory
      ? cart.items.some(item => item.menuItemId === menuItemId && item.selectedSubcategory?.title === selectedSubcategory.title)
      : cart.items.some(item => item.menuItemId === menuItemId);

    if (!itemExists) {
      console.warn('Attempted to remove non-existent item:', menuItemId, selectedSubcategory ? `with subcategory ${selectedSubcategory.title}` : '');
      // Don't show an error to the user since the end result is the same (item not in cart)
      return;
    }

    // Optimistically update the UI
    setCart(prevCart => ({
      ...prevCart,
      items: selectedSubcategory
        ? prevCart.items.filter(item => !(item.menuItemId === menuItemId && item.selectedSubcategory?.title === selectedSubcategory.title))
        : prevCart.items.filter(item => item.menuItemId !== menuItemId)
    }));

    // Sync with server or local storage
    try {
      if (useLocalStorage) {
        console.log('Saving updated cart to local storage');
        cartSync.saveLocalCart(cart);
      } else {
        console.log('Syncing removal with server...');
        const response = await cartService.removeFromCart(
          menuItemId,
          undefined, // token will be handled by the service
          selectedSubcategory?.title // plate parameter
        );

        if (response.success && response.data) {
          // Update cart with server response to ensure consistency
          const serverCart = response.data;
          serverCart.userId = cart.userId;
          setCart(serverCart);
          console.log('Cart updated from server after removal');
        } else {
          console.warn('Server remove failed, keeping optimistic update');
        }
      }
    } catch (error) {
      console.error('Error syncing cart removal:', error);
      if (!useLocalStorage) {
        setUseLocalStorage(true);
        cartSync.saveLocalCart(cart);
      }
    }
  };

  const clearCart = async () => {
    // Optimistically clear the cart
    setCart(prevCart => ({ ...prevCart, items: [] }));

    // Sync with server or local storage
    try {
      if (useLocalStorage) {
        console.log('Saving cleared cart to local storage');
        cartSync.saveLocalCart({ userId: cart.userId, items: [] });
      } else {
        console.log('Syncing cart clear with server...');
        const response = await cartService.clearCart();

        if (response.success && response.data) {
          // Update cart with server response to ensure consistency
          const serverCart = response.data;
          serverCart.userId = cart.userId;
          setCart(serverCart);
          console.log('Cart cleared on server');
        } else {
          console.warn('Server clear failed, keeping optimistic update');
        }
      }
    } catch (error) {
      console.error('Error syncing cart clear:', error);
      if (!useLocalStorage) {
        setUseLocalStorage(true);
        cartSync.saveLocalCart({ userId: cart.userId, items: [] });
      }
    }
  };

  const reorderCartItems = async (items: CartItem[]) => {
    console.log('Reordering cart items:', items);

    // Validate inputs
    if (!Array.isArray(items)) {
      setError('Invalid items array for reordering.');
      console.error('Invalid items array in reorderCartItems');
      return;
    }

    // Set loading state
    setLoading(true);
    setError(null);

    // Optimistically update the UI
    setCart(prevCart => ({
      ...prevCart,
      items: items.map((item, index) => ({
        ...item,
        order: index
      }))
    }));

    // Sync with server or local storage
    try {
      if (useLocalStorage) {
        console.log('Saving reordered cart to local storage');
        const updatedCart = {
          ...cart,
          items: items.map((item, index) => ({
            ...item,
            order: index
          }))
        };
        cartSync.saveLocalCart(updatedCart);
      } else {
        console.log('Syncing cart reorder with server...');

        // Prepare the item orders for the API
        const itemOrders = items.map((item, index) => ({
          menuItemId: item.menuItemId,
          subcategoryTitle: item.selectedSubcategory.title,
          order: index
        }));

        const response = await cartService.reorderCartItems(itemOrders);

        if (response.success && response.data) {
          // Update cart with server response to ensure consistency
          const serverCart = response.data;
          serverCart.userId = cart.userId;
          setCart(serverCart);
          console.log('Cart reordered on server');
        } else {
          console.warn('Server reorder failed, keeping optimistic update');
        }
      }
    } catch (error) {
      console.error('Error syncing cart reorder:', error);
      if (!useLocalStorage) {
        console.log('Server failed, falling back to local storage');
        setUseLocalStorage(true);
        const updatedCart = {
          ...cart,
          items: items.map((item, index) => ({
            ...item,
            order: index
          }))
        };
        cartSync.saveLocalCart(updatedCart);
      }
    } finally {
      setLoading(false);
    }
  };

  const reorderFromOrder = async (orderItems: any[]) => {
    console.log('Reordering from previous order:', orderItems);

    if (!Array.isArray(orderItems) || orderItems.length === 0) {
      setError('No items to reorder.');
      return;
    }

    setLoading(true);
    setError(null);

    let successCount = 0;
    let failCount = 0;

    try {
      // Add each item from the order to the cart
      for (const orderItem of orderItems) {
        try {
          await addToCart(
            orderItem.menuItemId,
            orderItem.quantity,
            orderItem.notes || '',
            orderItem.selectedSubcategory
          );
          successCount++;
        } catch (error) {
          console.error('Failed to add item to cart:', orderItem, error);
          failCount++;
        }
      }

      // Show success message
      if (successCount > 0) {
        toast.success(`${successCount} item${successCount > 1 ? 's' : ''} added to cart`, {
          description: failCount > 0 ? `${failCount} item${failCount > 1 ? 's' : ''} could not be added` : 'All items from your previous order have been added',
          duration: 4000
        });
      }

      if (failCount > 0 && successCount === 0) {
        setError('Failed to add items to cart. Some items may no longer be available.');
      }

    } catch (error) {
      console.error('Error reordering from order:', error);
      setError('Failed to reorder items. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateCartTotals = (): CartTotals => {
    const subtotal = cart.items.reduce((sum, item) => {
      // Use price from selectedSubcategory if available, fallback to item.price
      const itemPrice = item.selectedSubcategory?.price || item.price || 0;
      return sum + (itemPrice * item.quantity);
    }, 0);

    // Calculate fees based on subtotal - using fixed values to match cart page
    const platformFee = 5; // Fixed ₹5 platform fee
    const deliveryCharge = 20; // Fixed ₹20 delivery charge

    // Calculate 5% tax on subtotal + platform fee + delivery charge
    const taxableAmount = subtotal + platformFee + deliveryCharge;
    const tax = parseFloat((taxableAmount * 0.05).toFixed(2)); // 5% tax rate on all components with 2 decimal places

    // Calculate total
    const total = subtotal + platformFee + deliveryCharge + tax;

    return {
      subtotal,
      platformFee,
      deliveryCharge,
      tax,
      discount: 0, // Default discount is 0
      total
    };
  };

  const refreshCart = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const userId = isAuthenticated && user ? (user._id || user.phone) : 'guest-user';

      try {
        console.log('Refreshing cart from server...');
        const response = await cartService.getCart();

        if (response.success && response.data) {
          const serverCart = response.data;
          serverCart.userId = userId;
          setCart(serverCart);
          setUseLocalStorage(false);
        } else {
          throw new Error('Invalid server response');
        }
      } catch (serverErr) {
        console.error('Error refreshing cart from server, using local storage:', serverErr);
        setUseLocalStorage(true);

        const localCart = cartSync.getLocalCart();
        if (localCart && typeof localCart === 'object') {
          localCart.userId = userId;
        }
        setCart(localCart || { userId: '', items: [] });
      }
    } catch (err) {
      console.error('Error refreshing cart:', err);
      setError('Failed to refresh cart');
    } finally {
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  return (
    <CartContext.Provider value={{
      cart,
      loading,
      error,
      isOnline: !useLocalStorage, // isOnline is the opposite of useLocalStorage
      addToCart,
      updateCartItem,
      removeFromCart,
      clearCart,
      reorderCartItems,
      reorderFromOrder,
      calculateCartTotals,
      refreshCart
    }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
