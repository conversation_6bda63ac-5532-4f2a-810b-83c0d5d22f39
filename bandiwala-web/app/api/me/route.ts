import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// In-memory user store for testing
// Using global object to persist between requests
const globalUsers = global as unknown as {
  users?: Record<string, any>;
};

// Initialize users if not already initialized
if (!globalUsers.users) {
  globalUsers.users = {};
}

const users = globalUsers.users;

// Helper function to get user from token
async function getUserFromToken(token: string) {
  try {
    // Try to call the backend first
    try {
      const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/me`;
      console.log('API route /api/me: Trying to call backend at:', backendUrl);

      // Log the JWT secret for debugging
      const jwtSecret = process.env.JWT_SECRET || 'bandiwala123dfghadfsjtktfgdsaDGFHGJHKJJDSAFGHJ';
      console.log('API route /api/me: Using JWT secret:', jwtSecret.substring(0, 10) + '...');

      const response = await fetch(backendUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      console.log('API route /api/me: Backend response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('API route /api/me: Backend response data:', data);

        // Only allow users with "user" role or no role (defaults to "user")
        if (data.success && data.user && data.user.role && data.user.role !== "user") {
          console.log('API route /api/me: User has non-user role:', data.user.role, 'Rejecting access');
          return {
            success: false,
            message: 'Access denied: This application is only for regular users'
          };
        }

        return { success: true, data };
      }

      console.log('API route /api/me: Backend call failed, using fallback');
    } catch (error) {
      console.error('API route /api/me: Error calling backend:', error);
      console.log('API route /api/me: Using fallback mechanism');
    }

    // If backend call fails, create a mock user instead of trying to verify the token
    console.log('API route /api/me: Creating mock user instead of verifying token');

    try {
      // Try to decode the token without verification
      // This is just to extract the payload, not for security
      const base64Payload = token.split('.')[1];
      const payload = JSON.parse(Buffer.from(base64Payload, 'base64').toString());
      console.log('API route /api/me: Decoded token payload:', payload);

      // Use the id from the token if available (our backend uses 'id' not 'userId')
      const userId = payload.id || payload.userId || `user-${payload.email?.replace(/[^a-zA-Z0-9]/g, '') || Date.now()}`;

      // Only allow users with "user" role or no role (defaults to "user")
      const userRole = payload.role || 'user';
      if (userRole !== 'user') {
        console.log('API route /api/me: Token user has non-user role:', userRole, 'Rejecting access');
        return {
          success: false,
          message: 'Access denied: This application is only for regular users'
        };
      }

      // Check if we already have this user
      if (users[userId]) {
        console.log('API route /api/me: Found existing user:', users[userId]);
        // Ensure the stored user also has the correct role
        if (users[userId].role !== 'user') {
          console.log('API route /api/me: Stored user has non-user role:', users[userId].role, 'Rejecting access');
          return {
            success: false,
            message: 'Access denied: This application is only for regular users'
          };
        }
        return {
          success: true,
          data: {
            success: true,
            user: users[userId]
          }
        };
      }

      // Only create user if we have valid token data
      if (payload.phone || payload.email) {
        users[userId] = {
          _id: userId,
          phone: payload.phone || '',
          name: payload.name || '',
          email: payload.email || '',
          role: 'user', // Always set to 'user' since we've validated it above
          address: payload.address || '',
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        console.log('API route /api/me: Created user from token payload:', users[userId]);

        return {
          success: true,
          data: {
            success: true,
            user: users[userId]
          }
        };
      } else {
        console.log('API route /api/me: Invalid token payload, no phone or email found');
        return {
          success: false,
          data: {
            success: false,
            message: 'Invalid token - no user data found'
          }
        };
      }
    } catch (decodeError) {
      console.error('API route /api/me: Error decoding token:', decodeError);

      return {
        success: false,
        data: {
          success: false,
          message: 'Invalid token - could not decode'
        }
      };
    }
  } catch (error) {
    console.error('API route /api/me: Unexpected error:', error);

    return {
      success: false,
      data: {
        success: false,
        message: 'Server error'
      }
    };
  }
}

export async function GET(req: Request) {
  try {
    console.log('API route /api/me: GET request received');

    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    console.log('API route /api/me: Authorization header:', authHeader ? 'Present' : 'Not present');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('API route /api/me: No valid authorization header');
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract the token
    const token = authHeader.split(' ')[1];
    console.log('API route /api/me: Token received:', token.substring(0, 10) + '...');

    // Get user from token
    const result = await getUserFromToken(token);

    if (result.success) {
      return NextResponse.json(result.data);
    } else {
      return NextResponse.json(
        { success: false, message: 'Authentication failed' },
        { status: 401 }
      );
    }
  } catch (error) {
    console.error('Error in GET /api/me:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}

export async function PUT(req: Request) {
  try {
    console.log('API route /api/me: PUT request received');

    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    console.log('API route /api/me: Authorization header:', authHeader ? 'Present' : 'Not present');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('API route /api/me: No valid authorization header');
      return NextResponse.json(
        { success: false, message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Extract the token
    const token = authHeader.split(' ')[1];
    console.log('API route /api/me: Token received:', token.substring(0, 10) + '...');

    // Get user from token
    const result = await getUserFromToken(token);

    if (!result.success) {
      console.log('API route /api/me: Token verification failed for update operation');
      return NextResponse.json(
        { success: false, message: 'Authentication failed' },
        { status: 401 }
      );
    }

    // Get the user data
    const userData = result.data.user;

    // Parse the request body
    const body = await req.json();
    console.log('API route /api/me: Update request body:', body);

    // Try to update the user in the backend first
    try {
      const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/profile`;
      console.log('API route /api/me: Trying to update user in backend at:', backendUrl);

      const response = await fetch(backendUrl, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(body)
      });

      console.log('API route /api/me: Backend update response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('API route /api/me: Backend update response data:', data);
        return NextResponse.json(data);
      }

      console.log('API route /api/me: Backend update failed, using fallback');
    } catch (error) {
      console.error('API route /api/me: Error updating in backend:', error);
      console.log('API route /api/me: Using fallback update mechanism');
    }

    // If backend update fails, update the user in our in-memory store
    const userId = userData._id;
    if (userId && users[userId]) {
      // Update the user data
      users[userId] = {
        ...users[userId],
        ...body,
        updatedAt: new Date()
      };

      console.log('API route /api/me: Updated user in memory store:', users[userId]);

      return NextResponse.json({
        success: true,
        message: 'Profile updated successfully',
        user: users[userId]
      });
    }

    // If we can't find the user, create a new one
    console.log('API route /api/me: Creating new user for update operation');

    const newUserId = `user-${Date.now()}`;
    users[newUserId] = {
      _id: newUserId,
      ...body,
      phone: userData.phone || '1234567890',
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('API route /api/me: Created and updated new user:', users[newUserId]);

    return NextResponse.json({
      success: true,
      message: 'Profile created and updated successfully',
      user: users[newUserId]
    });
  } catch (error) {
    console.error('Error in PUT /api/me:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
