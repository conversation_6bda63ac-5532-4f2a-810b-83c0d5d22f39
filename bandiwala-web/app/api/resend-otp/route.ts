import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    console.log('API route /api/resend-otp: Request received');

    // Parse the request body
    const body = await req.json();
    const { email, phone, verificationMethod } = body;

    console.log('API route /api/resend-otp: Request body:', { email, phone, verificationMethod });

    if (!email || !phone || !verificationMethod) {
      console.error('API route /api/resend-otp: Email, phone, and verificationMethod are required');
      return NextResponse.json(
        { success: false, message: 'Email, phone, and verificationMethod are required' },
        { status: 400 }
      );
    }

    // Call backend API to resend OTP
    const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/resend-otp`;
    console.log('API route /api/resend-otp: Calling backend at:', backendUrl);

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({ email, phone, verificationMethod })
    });

    console.log('API route /api/resend-otp: Backend response status:', response.status);

    const data = await response.json();
    console.log('API route /api/resend-otp: Backend response data:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in /api/resend-otp:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
