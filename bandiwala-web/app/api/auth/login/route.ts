import { NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// In-memory user store for testing
// Using global object to persist between requests
const globalUsers = global as unknown as {
  users?: Record<string, any>;
};

// Initialize users if not already initialized
if (!globalUsers.users) {
  globalUsers.users = {};
}

const users = globalUsers.users;

export async function POST(req: Request) {
  try {
    console.log('API route /api/auth/login: Request received');

    // Parse the request body
    const body = await req.json();
    const { email, phone, password } = body;

    console.log('API route /api/auth/login: Request body:', { email, phone, password: '***' });

    // Check if either email or phone is provided along with password
    if ((!email && !phone) || !password) {
      console.error('API route /api/auth/login: Email/Phone and password are required');
      return NextResponse.json(
        { success: false, message: 'Email/Phone and password are required' },
        { status: 400 }
      );
    }

    // Try to call the backend first
    try {
      const backendUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/users/login`;
      console.log('API route /api/auth/login: Trying to call backend at:', backendUrl);

      // Prepare request body for backend
      const requestBody: any = { password };
      if (email) {
        requestBody.email = email;
      } else if (phone) {
        requestBody.phone = phone;
      }

      const response = await fetch(backendUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      console.log('API route /api/auth/login: Backend response status:', response.status);

      if (response.ok) {
        const data = await response.json();
        console.log('API route /api/auth/login: Backend response data:', data);
        return NextResponse.json(data);
      } else {
        // Handle backend error responses
        const errorData = await response.json();
        console.log('API route /api/auth/login: Backend error response:', errorData);

        // If it's a 404 (user not found), return the specific error message
        if (response.status === 404) {
          return NextResponse.json(
            { success: false, message: errorData.message, needsRegistration: true },
            { status: 404 }
          );
        }

        // For other errors, return the backend error message
        if (errorData.message) {
          return NextResponse.json(
            { success: false, message: errorData.message },
            { status: response.status }
          );
        }
      }

      console.log('API route /api/auth/login: Backend call failed, using fallback');
    } catch (error) {
      console.error('API route /api/auth/login: Error calling backend:', error);
      console.log('API route /api/auth/login: Using fallback mechanism');
    }

    // If backend call fails, use our fallback login mechanism
    console.log('API route /api/auth/login: Using fallback login mechanism');

    // For demo purposes, we'll use a fixed password for all users
    // In a real app, you would use proper password hashing and storage
    const DEMO_PASSWORD = 'bandiwala123';

    // Check if the provided password matches our demo password
    if (password !== DEMO_PASSWORD) {
      console.log('API route /api/auth/login: Password mismatch, expected:', DEMO_PASSWORD);
      const identifier = email ? 'email' : 'phone number';
      return NextResponse.json(
        { success: false, message: `Invalid ${identifier} or password` },
        { status: 401 }
      );
    }

    let userId: string;
    let userName: string;
    let userEmail: string;
    let userPhone: string;

    if (email) {
      // Extract name from email if possible
      userName = email.split('@')[0]
        .split('.')
        .map((part: string) => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ');

      // Create a user ID from the email
      userId = `user-${email.replace(/[^a-zA-Z0-9]/g, '')}`;
      userEmail = email;
      // Generate a random phone number for email-based users
      userPhone = '9' + Math.floor(Math.random() * 1000000000).toString().padStart(9, '0');
    } else {
      // Phone-based login
      // Create a user ID from the phone number
      userId = `user-${phone.replace(/\D/g, '')}`;
      userPhone = phone;
      // Don't generate placeholder email for phone-based users - leave it empty
      userEmail = '';
      // Use a more user-friendly default name
      userName = 'User';
    }

    // Check if user already exists
    if (!users[userId]) {
      // For fallback mechanism, suggest registration for new users
      const identifier = email ? 'email' : 'phone number';
      console.log(`API route /api/auth/login: User not found with ${identifier}, suggesting registration`);
      return NextResponse.json(
        {
          success: false,
          message: `No account found with this ${identifier}. Please register to create an account.`,
          needsRegistration: true
        },
        { status: 404 }
      );
    } else {
      // Update the existing user's last login
      users[userId].updatedAt = new Date();
      console.log('API route /api/auth/login: Updated existing user:', users[userId]);
    }

    // Generate JWT token
    // Use the same JWT secret as the backend
    const jwtSecret = process.env.JWT_SECRET || 'bandiwala123dfghadfsjtktfgdsaDGFHGJHKJJDSAFGHJ';
    const token = jwt.sign(
      {
        userId: users[userId]._id,
        email: users[userId].email,
        phone: users[userId].phone,
        name: users[userId].name,
        role: users[userId].role
      },
      jwtSecret,
      { expiresIn: '7d' }
    );

    console.log('API route /api/auth/login: Login successful, returning token');

    return NextResponse.json({
      success: true,
      message: 'Login successful',
      token,
      user: users[userId]
    });
  } catch (error) {
    console.error('Error in /api/auth/login:', error);
    return NextResponse.json(
      { success: false, message: 'Server error' },
      { status: 500 }
    );
  }
}
